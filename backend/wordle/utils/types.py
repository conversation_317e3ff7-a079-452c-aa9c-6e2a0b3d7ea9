
from __future__ import annotations
from typing import Tuple

WORD_LEN = 5
Color = str         # 'G' | 'Y' | 'B'
Pattern = str       # 'GYBBY'
HistoryEntry = Tuple[str, Pattern]

def assert_word(w: str):
    if not isinstance(w, str) or len(w) != WORD_LEN or not w.isalpha() or not w.islower():
        raise ValueError(f"word must be 5 lowercase letters: {w!r}")

def pattern_to_id(p: Pattern) -> int:
    m = {'B': 0, 'Y': 1, 'G': 2}
    x = 0
    for i, c in enumerate(reversed(p)):
        x += (3 ** i) * m[c]
    return x

def id_to_pattern(x: int) -> Pattern:
    m = {0: 'B', 1: 'Y', 2: 'G'}
    out = []
    for _ in range(WORD_LEN):
        out.append(m[x % 3]); x //= 3
    return ''.join(reversed(out))
