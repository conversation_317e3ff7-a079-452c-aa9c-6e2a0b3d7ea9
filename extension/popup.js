const statusEl = document.getElementById("status");
const whoEl = document.getElementById("who");
const loginBtn = document.getElementById("login");
const logoutBtn = document.getElementById("logout");
const openPanelBtn = document.getElementById("open-panel");
const hardEl = document.getElementById("hard");
const kEl = document.getElementById("k");

function setStatus(msg) { statusEl.textContent = msg || ""; }
function setUser(email) {
  whoEl.textContent = email ? `Signed in as ${email}` : "Not signed in";
  loginBtn.style.display = email ? "none" : "inline-block";
  logoutBtn.style.display = email ? "inline-block" : "none";
}

chrome.storage.local.get(["email"], ({email}) => setUser(email || null));

loginBtn.addEventListener("click", () => {
  setStatus("Opening Google sign-in…");
  chrome.runtime.sendMessage({ type: "LOGIN" }, (res) => {
    if (!res || !res.ok) {
      setStatus("Login failed: " + (res && res.error ? res.error : "unknown"));
      return;
    }
    setUser(res.email || null);
    setStatus("Logged in.");
  });
});

logoutBtn.addEventListener("click", () => {
  chrome.runtime.sendMessage({ type: "LOGOUT" }, () => {
    setUser(null);
    setStatus("Logged out.");
  });
});

openPanelBtn.addEventListener("click", async () => {
  const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
  if (!tab || !tab.id) return setStatus("Open the Wordle page first.");
  chrome.tabs.sendMessage(tab.id, { type: "OPEN_PANEL", hard: hardEl.checked, k: Number(kEl.value) });
});
