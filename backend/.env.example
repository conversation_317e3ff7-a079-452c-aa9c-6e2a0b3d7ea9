# Backend configuration
BACKEND_PUBLIC_URL=http://localhost:8000
JWT_SECRET=DNIyXMBoGA6yIn9/HLJ8DVrLkYUKT5lWaeoLAfyuZNc=

# CORS: Chrome extension ID (from chrome://extensions after loading unpacked)
EXTENSION_ID=your_extension_id_herelbjonoogmgpjlpiigacopkopkjphhadp

# Google OAuth (create OAuth client in Google Cloud Console)
GOOGLE_CLIENT_ID=5202757986-22r9sk3ivn5egphn3m6dqh0amcdsfo4q.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=yGOCSPX-QfhJJXNcjKu2Pqfn9V59rqHeiX2Z
GOOGLE_OAUTH_SCOPES=<EMAIL>
