
from __future__ import annotations
from collections import Counter
from typing import List
from .utils.types import Pattern, Color, WORD_LEN, assert_word

def evaluate_guess(answer: str, guess: str) -> Pattern:
    assert_word(answer); assert_word(guess)
    out: List[Color] = ['B'] * WORD_LEN
    need: Counter = Counter()

    for i in range(WORD_LEN):
        if guess[i] == answer[i]:
            out[i] = 'G'
        else:
            need[answer[i]] += 1

    for i in range(WORD_LEN):
        if out[i] == 'G':
            continue
        ch = guess[i]
        if need[ch] > 0:
            out[i] = 'Y'; need[ch] -= 1
        else:
            out[i] = 'B'
    return ''.join(out)
