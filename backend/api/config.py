
from pydantic_settings import BaseSettings
from functools import lru_cache

class Settings(BaseSettings):
    BACKEND_PUBLIC_URL: str = "http://localhost:8000"
    JWT_SECRET: str = "dev_secret_change_me"
    EXTENSION_ID: str = ""

    GOOGLE_CLIENT_ID: str = ""
    GOOGLE_CLIENT_SECRET: str = ""
    GOOGLE_OAUTH_SCOPES: str = "openid email profile"

    # Allow localhost extension dev without setting EXTENSION_ID by falling back to this list
    CORS_EXTRA_ORIGINS: str = ""  # comma-separated

    class Config:
        env_file = ".env"

@lru_cache
def get_settings() -> Settings:
    return Settings()
