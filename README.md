# Wordle Helper — End-to-End (FastAPI + Chrome Extension)

This project gives you a working **Wordle solver** with:
- A **FastAPI** backend (Google OAuth, JWT, solver endpoints)
- A **Chrome extension (MV3)** that runs on the official Wordle page, lets you enter your history, and calls the backend for suggestions

## Quick Start

### 0) Create Google OAuth Client
1. In Google Cloud Console, create an **OAuth 2.0 Client ID** of type **Web application**.
2. Authorized redirect URIs:
   - `http://localhost:8000/auth/google/callback`
   - `https://<YOUR_EXTENSION_ID>.chromiumapp.org/`  ← you'll set this after loading the extension once
3. Copy the **Client ID** and **Client Secret**.

### 1) Backend
```
cd backend
python -m venv .venv && source .venv/bin/activate
pip install -r requirements.txt
cp .env.example .env
# Edit .env:
# - BACKEND_PUBLIC_URL=http://localhost:8000
# - JWT_SECRET=<random string>
# - EXTENSION_ID=<your extension id>  (from chrome://extensions once loaded)
# - GOOGLE_CLIENT_ID=...
# - GOOGLE_CLIENT_SECRET=...
uvicorn main:app --reload
```
Backend will run on `http://localhost:8000`.

> Tip: To avoid "Candidates: 0" during testing, add a real `wordle/data/answers.txt` list (one 5-letter word per line).
> By default, a small sample list is used.

### 2) Chrome Extension (MV3)
- Go to `chrome://extensions`
- Enable **Developer mode**
- Click **Load unpacked** and select the `extension/` folder.

Now click the extension icon → **Open popup** → **Sign in with Google**.
After you sign in, open the Wordle page (NYTimes). Click **Open Helper Panel** in the popup. Enter guesses + patterns and click **Suggest**.

### How OAuth Flow Works (dev UX)
- The extension asks the backend to start Google OAuth (`/auth/google/start`).
- Google returns to backend `/auth/google/callback`.
- Backend creates a JWT and **redirects to** `https://<EXT_ID>.chromiumapp.org/provider_cb#token=...`.
- `chrome.identity.launchWebAuthFlow` captures this final redirect and returns the URL to the extension.
- The extension extracts and stores the JWT. All API calls send `Authorization: Bearer <JWT>`.

### Solver Notes
- Info-theoretic ranking (pattern entropy heuristic) + expected remaining set size as tie-break.
- Correct Wordle feedback (duplicate-safe).
- Two suggestion modes exposed via one endpoint: word ranking and letter ranking.

### Folder Structure
```
backend/
  api/
    auth.py        # Google OAuth + JWT
    config.py      # env settings
    models.py      # SQLite user model
    solver_api.py  # /api/suggest endpoint
  wordle/          # modular solver
    utils/
      types.py
      stats.py
    feedback.py
    candidates.py
    scoring.py
    suggest.py
    engine.py
    data/
      answers.sample.txt  # tiny demo list
  main.py
  requirements.txt
  .env.example
extension/
  manifest.json
  background.js
  popup.html
  popup.js
  contentScript.js
```

### Security & Local Dev
- JWT is issued by your backend; keep `JWT_SECRET` safe.
- For local dev, it's fine to run the backend on `http://localhost:8000`.
- Add CORS permissions if you host elsewhere.

### Extending
- Add `/api/filter`, `/api/solve` endpoints, or switch to exact mutual information scoring.
- Precompute pattern tables for speed with full word lists.
- Auto-read Wordle guesses from the DOM (possible but fiddly due to Shadow DOM). The panel supports manual entry now.

Enjoy!
