
from __future__ import annotations
from collections import Counter, defaultdict
from math import log
from typing import Dict, Iterable, List, Sequence

def entropy(ps: Iterable[float]) -> float:
    s = 0.0
    for p in ps:
        if p > 0.0:
            s -= p * log(p)
    return s

def binary_entropy(p: float) -> float:
    if p <= 0.0 or p >= 1.0:
        return 0.0
    return -(p * log(p) + (1 - p) * log(1 - p))

def letter_presence_probs(candidates: Sequence[str]) -> Dict[str, float]:
    n = len(candidates)
    if n == 0:
        return {chr(97+i): 0.0 for i in range(26)}
    counts = Counter()
    for a in candidates:
        for ch in set(a):
            counts[ch] += 1
    return {chr(97+i): counts.get(chr(97+i), 0) / n for i in range(26)}

def positional_probs(candidates: Sequence[str]) -> List[Dict[str, float]]:
    n = len(candidates)
    if n == 0:
        return [dict() for _ in range(5)]
    mats = [defaultdict(float) for _ in range(5)]
    for a in candidates:
        for i, ch in enumerate(a):
            mats[i][ch] += 1.0
    for i in range(5):
        for ch in list(mats[i].keys()):
            mats[i][ch] /= n
    return [dict(m) for m in mats]
