
from pathlib import Path
from wordle import evaluate_guess, filter_candidates, suggest_words

DATA = Path(__file__).parents[1] / "wordle" / "data"
ANS_PATH = DATA / "answers.txt"
SAMPLE_PATH = DATA / "answers.sample.txt"
ANS = [w.strip() for w in (ANS_PATH if ANS_PATH.exists() else SAMPLE_PATH).read_text().splitlines()]

def play(secret: str, first_guess="stare", max_steps=6):
    history = []
    for step in range(1, max_steps+1):
        C = filter_candidates(ANS, history)
        guess = first_guess if step==1 else suggest_words(C, guesses=C, k=1)[0][0]
        pat = evaluate_guess(secret, guess)
        print(f"#{step} guess={guess} pattern={pat} candidates={len(C)}")
        if pat == "GGGGG":
            return step
        history.append((guess, pat))

if __name__ == "__main__":
    for secret in ["awake","bench","model"]:
        print("\n==", secret)
        steps = play(secret)
        print("Solved in:", steps)
