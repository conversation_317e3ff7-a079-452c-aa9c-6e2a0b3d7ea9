
from __future__ import annotations
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from .api.config import get_settings
from .api import auth as auth_routes
from .api import solver_api as solver_routes


app = FastAPI(title="Wordle Helper API")

settings = get_settings()

# CORS settings: allow extension origin if provided
origins = []
if settings.EXTENSION_ID:
    origins.append(f"chrome-extension://{settings.EXTENSION_ID}")
extra = [o.strip() for o in settings.CORS_EXTRA_ORIGINS.split(",") if o.strip()]
origins.extend(extra)
origins.append("http://localhost:5173")  # optional local dev UI
origins.append(settings.BACKEND_PUBLIC_URL)

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(auth_routes.router)
app.include_router(solver_routes.router)

@app.get("/healthz")
def healthz():
    return {"ok": True}
