
from __future__ import annotations
from typing import List, Sequence
from .utils.types import HistoryEntry
from .feedback import evaluate_guess

def is_consistent(candidate: str, history: Sequence[HistoryEntry]) -> bool:
    return all(evaluate_guess(candidate, g) == p for g, p in history)

def filter_candidates(answers: Sequence[str], history: Sequence[HistoryEntry]) -> List[str]:
    return [a for a in answers if is_consistent(a, history)]
