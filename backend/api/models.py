
from __future__ import annotations
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from typing import Optional

class Base(DeclarativeBase):
    pass

class User(Base):
    __tablename__ = "users"
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    sub: Mapped[str] = mapped_column(unique=True, index=True)  # Google 'sub' (stable user id)
    email: Mapped[Optional[str]]
    name: Mapped[Optional[str]]
    picture: Mapped[Optional[str]]

def make_engine(db_url: str = "sqlite:///./app.db"):
    return create_engine(db_url, connect_args={"check_same_thread": False})

def make_session(engine):
    return sessionmaker(bind=engine, autoflush=False, autocommit=False)
