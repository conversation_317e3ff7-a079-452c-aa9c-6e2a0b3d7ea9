
from __future__ import annotations
import os, base64, json, time
from typing import Optional, <PERSON>ple
import httpx
import jwt
from fastapi import APIRouter, Depends, HTTPException, Request, Response
from fastapi.responses import HTMLResponse, RedirectResponse
from sqlalchemy.orm import Session

from .config import get_settings
from .models import User, make_engine, make_session, Base

router = APIRouter(prefix="/auth", tags=["auth"])

engine = make_engine()
SessionLocal = make_session(engine)
Base.metadata.create_all(engine)

GOOGLE_AUTH_URL = "https://accounts.google.com/o/oauth2/v2/auth"
GOOGLE_TOKEN_URL = "https://oauth2.googleapis.com/token"
GOOGLE_USERINFO = "https://openidconnect.googleapis.com/v1/userinfo"

def _jwt_encode(payload: dict, secret: str) -> str:
    return jwt.encode(payload, secret, algorithm="HS256")

def _jwt_decode(token: str, secret: str) -> dict:
    return jwt.decode(token, secret, algorithms=["HS256"])

def _compute_extension_redirect(ext_id: str) -> str:
    # Chrome identity uses https://<EXTENSION_ID>.chromiumapp.org/ as the redirect origin
    return f"https://{ext_id}.chromiumapp.org/provider_cb"

@router.get("/google/start")
async def google_start():
    settings = get_settings()
    if not settings.GOOGLE_CLIENT_ID:
        raise HTTPException(500, "GOOGLE_CLIENT_ID not configured")
    params = {
        "client_id": settings.GOOGLE_CLIENT_ID,
        "redirect_uri": f"{settings.BACKEND_PUBLIC_URL}/auth/google/callback",
        "response_type": "code",
        "scope": settings.GOOGLE_OAUTH_SCOPES,
        "access_type": "offline",
        "include_granted_scopes": "true",
        "prompt": "consent",
    }
    # Build URL
    from urllib.parse import urlencode
    return RedirectResponse(f"{GOOGLE_AUTH_URL}?{urlencode(params)}")

@router.get("/google/callback")
async def google_callback(request: Request):
    settings = get_settings()
    code = request.query_params.get("code")
    if not code:
        raise HTTPException(400, "Missing code")
    # exchange code for tokens
    data = {
        "code": code,
        "client_id": settings.GOOGLE_CLIENT_ID,
        "client_secret": settings.GOOGLE_CLIENT_SECRET,
        "redirect_uri": f"{settings.BACKEND_PUBLIC_URL}/auth/google/callback",
        "grant_type": "authorization_code",
    }
    async with httpx.AsyncClient(timeout=20) as client:
        token_res = await client.post(GOOGLE_TOKEN_URL, data=data)
        if token_res.status_code != 200:
            raise HTTPException(400, f"Token exchange failed: {token_res.text}")
        tokens = token_res.json()
        id_token = tokens.get("id_token")
        access_token = tokens.get("access_token")
        if not access_token:
            raise HTTPException(400, "No access_token from Google")

        # fetch userinfo
        headers = {"Authorization": f"Bearer {access_token}"}
        userinfo_res = await client.get(GOOGLE_USERINFO, headers=headers)
        if userinfo_res.status_code != 200:
            raise HTTPException(400, f"userinfo failed: {userinfo_res.text}")
        info = userinfo_res.json()

    # upsert user
    with SessionLocal() as db:
        user = db.query(User).filter(User.sub == info["sub"]).first()
        if not user:
            user = User(sub=info["sub"], email=info.get("email"), name=info.get("name"), picture=info.get("picture"))
            db.add(user)
        else:
            user.email = info.get("email")
            user.name = info.get("name")
            user.picture = info.get("picture")
        db.commit()

    # issue our JWT
    now = int(time.time())
    payload = {"sub": info["sub"], "email": info.get("email"), "iat": now, "exp": now + 60*60*24*30}
    token = _jwt_encode(payload, settings.JWT_SECRET)

    # redirect to Chrome extension redirect URL with token in fragment
    ext_id = settings.EXTENSION_ID
    if not ext_id:
        # For dev without extension id, show the token on a page
        return HTMLResponse(f"""
        <html><body>
        <h3>Login success (dev)</h3>
        <p>Your JWT token (copy/paste into extension if needed):</p>
        <pre>{token}</pre>
        </body></html>
        """)
    redirect_url = _compute_extension_redirect(ext_id) + f"#token={token}&email={info.get('email','')}"
    return RedirectResponse(redirect_url)

def require_user(token: str | None) -> dict:
    settings = get_settings()
    if not token:
        raise HTTPException(401, "Missing Authorization")
    try:
        payload = _jwt_decode(token, settings.JWT_SECRET)
        return payload
    except Exception as e:
        raise HTTPException(401, f"Invalid token: {e}")
