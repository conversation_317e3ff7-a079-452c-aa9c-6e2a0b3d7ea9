// You are given a binary string s, and an integer k.
// In one operation, you must choose exactly k different indices and flip each '0' to '1' and each '1' to '0'.

// Return the minimum number of operations required to make all characters in the string equal to '1'. If it is not possible, return -1.

#include <iostream>
#include <string>
#include <vector>
#include <algorithm>
#include <queue>
#include <unordered_set>
#include <unordered_map>
#include <functional>
#include <numeric>
#include <climits>
using namespace std;

class Solution {
public:
    int minKBitFlips(string s, int k) {
        
    }
};

// Alternative O(1) space solution using bit manipulation
class OptimizedSolution {
public:
    int minKBitFlips(string s, int k) {
        int n = s.length();
        int operations = 0;
        int flipCount = 0;

        for (int i = 0; i < n; i++) {
            // If we're past k positions, remove the effect of the flip at i-k
            if (i >= k && s[i - k] > '1') {
                flipCount--;
            }

            // Check if current position needs to be flipped
            if ((s[i] - '0' + flipCount) % 2 == 0) {
                // Not enough positions left to flip k bits
                if (i + k > n) {
                    return -1;
                }

                // Mark this position as start of a flip operation
                s[i] = (char)(s[i] + 2); // '0' -> '2', '1' -> '3'
                flipCount++;
                operations++;
            }
        }

        return operations;
    }
};

// Test function
void testSolution() {
    Solution sol;
    OptimizedSolution optSol;

    // Test case from the problem
    string test1 = "0101";
    int k1 = 3;
    cout << "Test 1: s = \"" << test1 << "\", k = " << k1 << endl;
    cout << "Expected: 2, Got: " << sol.minKBitFlips(test1, k1) << endl;

    // Additional test cases
    string test2 = "001010";
    int k2 = 2;
    cout << "Test 2: s = \"" << test2 << "\", k = " << k2 << endl;
    cout << "Result: " << optSol.minKBitFlips(test2, k2) << endl;

    string test3 = "0001010";
    int k3 = 3;
    cout << "Test 3: s = \"" << test3 << "\", k = " << k3 << endl;
    cout << "Result: " << sol.minKBitFlips(test3, k3) << endl;

    string test4 = "111";
    int k4 = 2;
    cout << "Test 4: s = \"" << test4 << "\", k = " << k4 << endl;
    cout << "Expected: 0, Got: " << sol.minKBitFlips(test4, k4) << endl;

    // Edge cases
    string test5 = "0";
    int k5 = 1;
    cout << "Test 5: s = \"" << test5 << "\", k = " << k5 << endl;
    cout << "Expected: 1, Got: " << sol.minKBitFlips(test5, k5) << endl;

    string test6 = "00";
    int k6 = 3;
    cout << "Test 6: s = \"" << test6 << "\", k = " << k6 << endl;
    cout << "Expected: -1, Got: " << sol.minKBitFlips(test6, k6) << endl;

    string test7 = "010";
    int k7 = 2;
    cout << "Test 7: s = \"" << test7 << "\", k = " << k7 << endl;
    cout << "Result: " << sol.minKBitFlips(test7, k7) << endl;
}

int main() {
    testSolution();
    return 0;
}