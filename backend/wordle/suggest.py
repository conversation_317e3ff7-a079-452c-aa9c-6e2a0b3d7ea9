
from __future__ import annotations
from typing import List, Sequence, Tuple
from .scoring import score_guess_expected_info_and_size
from .utils.stats import letter_presence_probs, positional_probs, binary_entropy

def suggest_words(
    candidates: Sequence[str],
    guesses: Sequence[str] | None = None,
    k: int = 5,
    hard_mode: bool = True,
    penalize_repeats: float = 0.0,
) -> List[Tuple[str, float, float]]:
    if guesses is None:
        guesses = candidates if hard_mode else candidates
    scored: List[Tuple[str, float, float]] = []
    for g in guesses:
        h, exp_size = score_guess_expected_info_and_size(candidates, g)
        if penalize_repeats:
            dup_pen = len(g) - len(set(g))
            h -= penalize_repeats * dup_pen
        scored.append((g, h, exp_size))
    scored.sort(key=lambda t: (-t[1], t[2], t[0]))
    return scored[:k]

def suggest_letters(candidates: Sequence[str], k: int = 5):
    pres = letter_presence_probs(candidates)
    presence_rank = sorted(
        ((ch, pres[ch], binary_entropy(pres[ch])) for ch in pres),
        key=lambda t: (-t[2], -t[1], t[0])
    )[:k]
    pos = positional_probs(candidates)
    pos_ranked: List[List[Tuple[str, float]]] = []
    for i in range(5):
        pos_ranked.append(sorted(pos[i].items(), key=lambda t: (-t[1], t[0]))[:k])
    return presence_rank, pos_ranked
