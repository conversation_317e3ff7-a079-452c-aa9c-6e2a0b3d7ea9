// You are given a binary string s, and an integer k.
// In one operation, you must choose exactly k different indices and flip each '0' to '1' and each '1' to '0'.

// Return the minimum number of operations required to make all characters in the string equal to '1'. If it is not possible, return -1.

#include <iostream>
#include <string>
#include <vector>
#include <algorithm>
#include <queue>
#include <unordered_set>
#include <functional>
#include <numeric>
using namespace std;

class Solution {
public:
    int minKBitFlips(string s, int k) {
        int n = s.length();

        // Key insight: Each position can be flipped multiple times
        // We need to track the net effect of flips on each position

        // Count how many times each position needs to be flipped
        vector<int> flipCount(n, 0);
        int operations = 0;

        // For each position, if it's 0, we need odd number of flips
        // If it's 1, we need even number of flips
        for (int i = 0; i < n; i++) {
            int currentBit = s[i] - '0';
            int netFlips = flipCount[i];

            // Current effective bit after all flips
            int effectiveBit = (currentBit + netFlips) % 2;

            // If effective bit is 0, we need one more flip operation
            if (effectiveBit == 0) {
                // We can choose any k positions including this one
                // Optimal strategy: include current position and k-1 others

                // Check if we have enough positions to flip
                if (i + k > n) {
                    // Try to find if we can still make it work
                    // by using positions before current position
                    bool canFlip = false;

                    // Try all possible combinations that include position i
                    for (int start = max(0, i - k + 1); start <= i; start++) {
                        if (start + k <= n) {
                            canFlip = true;
                            // Apply flip from start to start+k-1
                            for (int j = start; j < start + k; j++) {
                                flipCount[j]++;
                            }
                            operations++;
                            break;
                        }
                    }

                    if (!canFlip) {
                        return -1;
                    }
                } else {
                    // Apply flip from i to i+k-1
                    for (int j = i; j < i + k; j++) {
                        flipCount[j]++;
                    }
                    operations++;
                }
            }
        }

        return operations;
    }
};

// Alternative O(1) space solution using bit manipulation
class OptimizedSolution {
public:
    int minKBitFlips(string s, int k) {
        int n = s.length();
        int operations = 0;
        int flipCount = 0;

        for (int i = 0; i < n; i++) {
            // If we're past k positions, remove the effect of the flip at i-k
            if (i >= k && s[i - k] > '1') {
                flipCount--;
            }

            // Check if current position needs to be flipped
            if ((s[i] - '0' + flipCount) % 2 == 0) {
                // Not enough positions left to flip k bits
                if (i + k > n) {
                    return -1;
                }

                // Mark this position as start of a flip operation
                s[i] = (char)(s[i] + 2); // '0' -> '2', '1' -> '3'
                flipCount++;
                operations++;
            }
        }

        return operations;
    }
};

// Test function
void testSolution() {
    Solution sol;
    OptimizedSolution optSol;

    // Test case from the problem
    string test1 = "0101";
    int k1 = 3;
    cout << "Test 1: s = \"" << test1 << "\", k = " << k1 << endl;
    cout << "Expected: 2, Got: " << sol.minKBitFlips(test1, k1) << endl;

    // Additional test cases
    string test2 = "001010";
    int k2 = 2;
    cout << "Test 2: s = \"" << test2 << "\", k = " << k2 << endl;
    cout << "Result: " << optSol.minKBitFlips(test2, k2) << endl;

    string test3 = "0001010";
    int k3 = 3;
    cout << "Test 3: s = \"" << test3 << "\", k = " << k3 << endl;
    cout << "Result: " << sol.minKBitFlips(test3, k3) << endl;

    string test4 = "111";
    int k4 = 2;
    cout << "Test 4: s = \"" << test4 << "\", k = " << k4 << endl;
    cout << "Expected: 0, Got: " << sol.minKBitFlips(test4, k4) << endl;
}

int main() {
    testSolution();
    return 0;
}