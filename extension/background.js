// Background service worker: handles Google OAuth via backend and stores JWT.
const BACKEND = "http://localhost:8000";

async function login() {
  // Start OAuth flow via backend which redirects to Google then back to backend,
  // then finally to https://<EXT_ID>.chromiumapp.org/provider_cb#token=...
  const startUrl = `${BACKEND}/auth/google/start`;
  return new Promise((resolve, reject) => {
    chrome.identity.launchWebAuthFlow(
      {
        url: startUrl,
        interactive: true,
      },
      (redirectUrl) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
          return;
        }
        if (!redirectUrl) {
          reject(new Error("No redirect URL"));
          return;
        }
        // Expect fragment with token, e.g., ...#token=<JWT>&email=<email>
        const hash = new URL(redirectUrl).hash.substring(1); // remove '#'
        const params = new URLSearchParams(hash);
        const token = params.get("token");
        const email = params.get("email");
        if (!token) {
          reject(new Error("No token in redirect"));
          return;
        }
        chrome.storage.local.set({ jwt: token, email }, () => resolve({ token, email }));
      }
    );
  });
}

async function getToken() {
  return new Promise((resolve) => {
    chrome.storage.local.get(["jwt"], (obj) => resolve(obj.jwt || null));
  });
}

chrome.runtime.onMessage.addListener((msg, sender, sendResponse) => {
  (async () => {
    if (msg.type === "LOGIN") {
      try {
        const res = await login();
        sendResponse({ ok: true, email: res.email });
      } catch (e) {
        sendResponse({ ok: false, error: String(e) });
      }
      return;
    }
    if (msg.type === "GET_TOKEN") {
      const tok = await getToken();
      sendResponse({ token: tok });
      return;
    }
    if (msg.type === "LOGOUT") {
      chrome.storage.local.remove(["jwt", "email"], () => sendResponse({ ok: true }));
      return;
    }
  })();
  return true; // keep the message channel open for async response
});
