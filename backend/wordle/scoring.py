
from __future__ import annotations
from typing import Sequence, Tuple
from .utils.types import pattern_to_id
from .feedback import evaluate_guess
from .utils.stats import entropy

def score_guess_expected_info_and_size(candidates: Sequence[str], guess: str) -> Tuple[float, float]:
    n = len(candidates)
    if n == 0:
        return (0.0, 0.0)
    buckets = [0] * (3 ** 5)
    for a in candidates:
        pid = pattern_to_id(evaluate_guess(a, guess))
        buckets[pid] += 1
    probs = [b / n for b in buckets if b]
    pat_entropy = entropy(probs)
    exp_size = sum(b * b for b in buckets) / float(n)
    return (pat_entropy, exp_size)
