
from __future__ import annotations
from typing import List, Tuple
from fastapi import APIRouter, Depends, HTTPException, Request
from pydantic import BaseModel, Field
from fastapi.middleware.cors import CORSMiddleware
from .config import get_settings
from .auth import require_user

from ..wordle.engine import filter_candidates, suggest_words, suggest_letters


router = APIRouter(prefix="/api", tags=["solver"])

class HistoryItem(BaseModel):
    guess: str = Field(..., min_length=5, max_length=5)
    pattern: str = Field(..., min_length=5, max_length=5, pattern=r"^[GYB]{5}$")

class SuggestRequest(BaseModel):
    history: List[HistoryItem] = []
    k: int = Field(5, ge=1, le=25)
    hard_mode: bool = True

class SuggestResponse(BaseModel):
    candidates: int
    words: List[tuple]  # (word, entropy, expected_size)
    letters: List[tuple]  # (letter, presence_prob, binary_entropy)
    positions: List[List[tuple]]

# In-memory word list for demo; real deployment should load answers.txt / guesses.txt
from pathlib import Path
DATA = Path(__file__).parents[1] / "wordle" / "data"
ANS_PATH = DATA / "answers.txt"
SAMPLE_PATH = DATA / "answers.sample.txt"
ANSWERS = [w.strip() for w in (ANS_PATH if ANS_PATH.exists() else SAMPLE_PATH).read_text().splitlines() if w.strip()]

@router.post("/suggest", response_model=SuggestResponse)
def suggest(req: SuggestRequest, request: Request):
    # TEMP: no auth for local testing
    history = [(h.guess.lower(), h.pattern.upper()) for h in req.history]
    cands = filter_candidates(ANSWERS, history)
    words = suggest_words(
        cands,
        guesses=cands if req.hard_mode else ANSWERS,
        k=req.k,
        hard_mode=req.hard_mode
    )
    pres, pos = suggest_letters(cands, k=req.k)
    return SuggestResponse(
        candidates=len(cands),
        words=words,
        letters=pres,
        positions=pos
    )
