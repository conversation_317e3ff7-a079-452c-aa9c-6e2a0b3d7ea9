// contentScript.js
console.log("[Wordle Helper] content script loaded on", location.href);

const BACKEND = "http://localhost:8000";

let state = { hard: true, k: 5 };

function createPanel() {
  if (document.getElementById("wordle-helper-panel")) return;

  const panel = document.createElement("div");
  panel.id = "wordle-helper-panel";
  panel.style.cssText = `
    position:fixed; top:12px; right:12px; z-index:999999;
    background:#fff; border:1px solid #ccc; border-radius:10px;
    padding:10px; width:320px; font:14px system-ui,-apple-system,Segoe UI,Roboto,sans-serif;
    box-shadow:0 8px 24px rgba(0,0,0,.2);
  `;
  panel.innerHTML = `
    <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:6px">
      <h3 style="margin:0;font-size:16px">Wordle Helper</h3>
      <button id="wh-close" title="Close" style="border:0;background:transparent;font-size:18px;cursor:pointer">✕</button>
    </div>
    <div style="color:#666;font-size:12px;margin-bottom:6px">Enter your past guesses (guess + G/Y/B pattern):</div>
    <div id="wh-rows"></div>
    <div style="display:flex;gap:6px;margin:6px 0">
      <input id="wh-guess" placeholder="stare" maxlength="5" style="flex:1;padding:6px 8px;border:1px solid #ccc;border-radius:8px;font-family:ui-monospace,Menlo,Consolas,monospace">
      <input id="wh-pattern" placeholder="BYGBB" maxlength="5" style="flex:1;padding:6px 8px;border:1px solid #ccc;border-radius:8px;font-family:ui-monospace,Menlo,Consolas,monospace">
      <button id="wh-add" style="padding:6px 10px;border:1px solid #ccc;border-radius:8px;background:#fff;cursor:pointer">Add</button>
    </div>
    <div style="display:flex;gap:10px;align-items:center;margin:6px 0">
      <label><input type="checkbox" id="wh-hard" checked> Hard mode</label>
      <label>k: <input id="wh-k" type="number" min="1" max="25" value="5" style="width:52px;padding:4px 6px;border:1px solid #ccc;border-radius:8px"></label>
      <button id="wh-suggest" style="margin-left:auto;padding:6px 10px;border:1px solid #ccc;border-radius:8px;background:#fff;cursor:pointer">Suggest</button>
    </div>
    <pre id="wh-out" style="max-height:200px;overflow:auto;background:#f7f7f8;padding:8px;border-radius:8px"></pre>
    <div id="wh-err" style="color:#b00;font-size:12px;white-space:pre-wrap;margin-top:6px"></div>
  `;
  document.body.appendChild(panel);

  const rowsEl = panel.querySelector("#wh-rows");
  const guessEl = panel.querySelector("#wh-guess");
  const patternEl = panel.querySelector("#wh-pattern");
  const addBtn = panel.querySelector("#wh-add");
  const hardEl = panel.querySelector("#wh-hard");
  const kEl = panel.querySelector("#wh-k");
  const suggestBtn = panel.querySelector("#wh-suggest");
  const outEl = panel.querySelector("#wh-out");
  const errEl = panel.querySelector("#wh-err");

  const history = [];

  function renderHistory() {
    rowsEl.innerHTML = "";
    history.forEach((h, i) => {
      const row = document.createElement("div");
      row.style.cssText = "display:flex;gap:8px;align-items:center;margin:4px 0;font-family:ui-monospace,Menlo,Consolas,monospace";
      row.innerHTML = `<span>${h.guess}</span><span>${h.pattern}</span>
        <button data-i="${i}" style="padding:2px 8px;border:1px solid #ccc;border-radius:8px;background:#fff;cursor:pointer">−</button>`;
      rowsEl.appendChild(row);
      row.querySelector("button").addEventListener("click", () => { history.splice(i,1); renderHistory(); });
    });
  }

  addBtn.addEventListener("click", () => {
    const g = String(guessEl.value || "").toLowerCase();
    const p = String(patternEl.value || "").toUpperCase();
    if (!/^[a-z]{5}$/.test(g) || !/^[GYB]{5}$/.test(p)) {
      errEl.textContent = "Use a 5-letter guess and a 5-letter pattern of G/Y/B.";
      return;
    }
    history.push({ guess: g, pattern: p });
    guessEl.value = ""; patternEl.value = ""; errEl.textContent = "";
    renderHistory();
  });

  panel.querySelector("#wh-close").addEventListener("click", () => panel.remove());

  // NO AUTH: plain fetch to /api/suggest
  suggestBtn.addEventListener("click", async () => {
    errEl.textContent = ""; outEl.textContent = "Thinking…";
    try {
      const k = Number(kEl.value) || 5;
      const hard = !!hardEl.checked;
      const res = await fetch(`${BACKEND}/api/suggest`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ history, k, hard_mode: hard }),
      });
      if (!res.ok) throw new Error(`HTTP ${res.status}: ${await res.text()}`);
      const data = await res.json();
      const words = data.words.map(([w,h,es]) => `${w}  (H=${h.toFixed(3)}  exp≈${es.toFixed(2)})`).join("\n");
      const letters = data.letters.map(([ch,p,ent]) => `${ch}  P=${p.toFixed(3)}  h=${ent.toFixed(3)}`).join("\n");
      outEl.textContent = `Candidates: ${data.candidates}\n\nWords:\n${words}\n\nLetters:\n${letters}`;
    } catch (e) {
      errEl.textContent = String(e);
      outEl.textContent = "";
    }
  });
}

// Listen for popup messages to open the panel
chrome.runtime.onMessage.addListener((msg, sender, sendResponse) => {
  if (msg.type === "OPEN_PANEL") {
    try { state.hard = !!msg.hard; state.k = Number(msg.k) || 5; } catch (_) {}
    createPanel();
    sendResponse({ ok: true });
  }
});
